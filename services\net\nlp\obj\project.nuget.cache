{"version": 2, "dgSpecHash": "ra5EX473unA=", "success": false, "projectFilePath": "C:\\Users\\<USER>\\Documents\\GitHub\\tno\\services\\net\\nlp\\TNO.Services.NLP.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "Microsoft.ML"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "System.Formats.Asn1"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "System.Text.Json"}, {"code": "NU1301", "level": "Error", "message": "The local source 'C:\\root\\.nuget\\packages' doesn't exist.", "libraryId": "TNO.Services"}]}